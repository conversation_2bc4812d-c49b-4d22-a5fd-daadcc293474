"""
AutoGen 编程工作流模拟版本
使用模拟响应演示完整的工作流程，无需真实 API 密钥
"""
import time
import random
from typing import Dict, List, Any


class MockAgent:
    """模拟 Agent 类"""
    
    def __init__(self, name: str, role: str, system_message: str):
        self.name = name
        self.role = role
        self.system_message = system_message
        self.responses = self._get_mock_responses()
    
    def _get_mock_responses(self) -> Dict[str, List[str]]:
        """获取模拟响应"""
        if self.role == "code_writer":
            return {
                "quick_sort": [
                    """我将为您编写一个快速排序函数：

```python
def quick_sort(arr):
    \"\"\"
    快速排序算法实现

    Args:
        arr (list): 待排序的列表

    Returns:
        list: 排序后的列表
    \"\"\"
    if len(arr) <= 1:
        return arr

    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]

    return quick_sort(left) + middle + quick_sort(right)

# 测试示例
if __name__ == "__main__":
    test_arr = [3, 6, 8, 10, 1, 2, 1]
    sorted_arr = quick_sort(test_arr)
    print(f"原数组: {test_arr}")
    print(f"排序后: {sorted_arr}")
```

这个实现使用了分治法的思想，选择中间元素作为基准值，将数组分为三部分：小于基准值、等于基准值和大于基准值的元素，然后递归排序左右两部分。"""
                ],
                "default": [
                    f"我是 {self.name}，专门负责编写高质量的代码。我将根据您的需求编写清晰、可读的代码，遵循最佳编程实践，并添加适当的注释和文档。"
                ]
            }
        elif self.role == "code_reviewer":
            return {
                "quick_sort": [
                    """代码审查意见：

✅ **优点：**
1. 代码逻辑清晰，易于理解
2. 包含了完整的文档字符串
3. 正确处理了边界情况（空列表和单元素列表）
4. 使用了列表推导式，代码简洁

⚠️ **改进建议：**
1. **类型注解**: 建议添加类型注解以提高代码可读性
2. **性能优化**: 当前实现会创建新的列表，内存使用较多
3. **输入验证**: 缺少对输入参数的验证
4. **错误处理**: 应该处理非数字类型的输入

🔧 **具体建议：**
- 添加 `from typing import List, Union` 导入
- 函数签名改为 `def quick_sort(arr: List[Union[int, float]]) -> List[Union[int, float]]:`
- 添加输入验证：检查是否为列表，元素是否可比较
- 考虑添加原地排序版本以节省内存

总体而言，这是一个很好的快速排序实现，建议按上述意见进行优化。"""
                ],
                "default": [
                    f"我是 {self.name}，专门负责代码审查。我将检查代码的正确性、可读性、性能和安全性，并提出具体的改进建议。"
                ]
            }
        elif self.role == "code_optimizer":
            return {
                "quick_sort": [
                    """根据审查建议，我对代码进行了优化：

```python
from typing import List, Union, TypeVar

T = TypeVar('T')

def quick_sort(arr: List[T]) -> List[T]:
    \"\"\"
    快速排序算法实现（优化版本）

    Args:
        arr (List[T]): 待排序的列表，元素必须支持比较操作

    Returns:
        List[T]: 排序后的新列表

    Raises:
        TypeError: 当输入不是列表或元素不支持比较时
        ValueError: 当列表为空时（可选检查）
    \"\"\"
    # 输入验证
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")

    if len(arr) <= 1:
        return arr.copy()  # 返回副本而不是原列表

    try:
        # 选择随机基准值以避免最坏情况
        import random
        pivot_index = random.randint(0, len(arr) - 1)
        pivot = arr[pivot_index]

        # 三路快排，处理重复元素更高效
        left = []
        middle = []
        right = []

        for x in arr:
            if x < pivot:
                left.append(x)
            elif x == pivot:
                middle.append(x)
            else:
                right.append(x)

        return quick_sort(left) + middle + quick_sort(right)

    except TypeError as e:
        raise TypeError(f"列表元素必须支持比较操作: {e}")

def quick_sort_inplace(arr: List[T], low: int = 0, high: int = None) -> None:
    \"\"\"
    原地快速排序（内存优化版本）

    Args:
        arr: 待排序的列表
        low: 起始索引
        high: 结束索引
    \"\"\"
    if high is None:
        high = len(arr) - 1

    if low < high:
        pi = partition(arr, low, high)
        quick_sort_inplace(arr, low, pi - 1)
        quick_sort_inplace(arr, pi + 1, high)

def partition(arr: List[T], low: int, high: int) -> int:
    \"\"\"分区函数\"\"\"
    pivot = arr[high]
    i = low - 1

    for j in range(low, high):
        if arr[j] <= pivot:
            i += 1
            arr[i], arr[j] = arr[j], arr[i]

    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1

# 测试和性能比较
if __name__ == "__main__":
    import time

    test_cases = [
        [3, 6, 8, 10, 1, 2, 1],
        [5, 5, 5, 5],  # 重复元素
        [1],           # 单元素
        [],            # 空列表
        list(range(1000, 0, -1))  # 逆序大数组
    ]

    for i, test_arr in enumerate(test_cases):
        print(f"\\n测试用例 {i+1}: {test_arr[:10]}{'...' if len(test_arr) > 10 else ''}")

        # 测试基础版本
        start_time = time.time()
        sorted_arr = quick_sort(test_arr.copy())
        end_time = time.time()
        print(f"优化版本结果: {sorted_arr[:10]}{'...' if len(sorted_arr) > 10 else ''}")
        print(f"执行时间: {end_time - start_time:.6f}秒")
```

**主要优化点：**
1. ✅ 添加了完整的类型注解和泛型支持
2. ✅ 增加了输入验证和错误处理
3. ✅ 使用随机基准值避免最坏情况
4. ✅ 提供了原地排序版本节省内存
5. ✅ 添加了性能测试代码
6. ✅ 改进了文档字符串

OPTIMIZATION_COMPLETE"""
                ],
                "default": [
                    f"我是 {self.name}，专门负责代码优化。我将根据原始代码和审查建议进行性能优化、结构改进，并确保代码质量。"
                ]
            }

    
    def generate_response(self, task_type: str = "default") -> str:
        """生成模拟响应"""
        responses = self.responses.get(task_type, self.responses["default"])
        return random.choice(responses)


class MockProgrammingWorkflow:
    """模拟编程工作流"""
    
    def __init__(self):
        self.agents = {
            "code_writer": MockAgent("CodeWriter", "code_writer", "编写代码"),
            "code_reviewer": MockAgent("CodeReviewer", "code_reviewer", "审查代码"),
            "code_optimizer": MockAgent("CodeOptimizer", "code_optimizer", "优化代码")
        }
        self.messages = []
    
    def run_programming_task(self, task_description: str) -> Dict[str, Any]:
        """运行编程任务"""
        print(f"🚀 开始编程工作流...")
        print(f"📝 任务描述: {task_description}")
        print("=" * 80)
        
        # 确定任务类型
        task_type = "quick_sort" if "快速排序" in task_description or "quick_sort" in task_description else "default"
        
        # 模拟三个 agent 的对话
        agents_order = ["code_writer", "code_reviewer", "code_optimizer"]
        
        for i, agent_key in enumerate(agents_order):
            agent = self.agents[agent_key]
            
            print(f"\n{'='*20} {agent.name} {'='*20}")
            
            # 模拟思考时间
            print("🤔 正在思考...")
            time.sleep(1 + random.uniform(0.5, 1.5))
            
            # 生成响应
            response = agent.generate_response(task_type)
            print(response)
            
            # 记录消息
            self.messages.append({
                "agent": agent.name,
                "role": agent.role,
                "content": response,
                "timestamp": time.time()
            })
            
            # 检查终止条件
            if "OPTIMIZATION_COMPLETE" in response:
                print(f"\n✅ 检测到终止条件: OPTIMIZATION_COMPLETE")
                break
        
        print("\n" + "=" * 80)
        print(f"✅ 工作流完成!")
        print(f"💬 总消息数: {len(self.messages)}")
        
        return {
            "messages": self.messages,
            "total_messages": len(self.messages),
            "task_type": task_type,
            "status": "completed"
        }
    
    def reset_workflow(self):
        """重置工作流"""
        self.messages = []
        print("🔄 工作流已重置")


def run_mock_examples():
    """运行模拟示例"""
    print("🎭 AutoGen 编程工作流模拟演示")
    print("=" * 80)
    
    workflow = MockProgrammingWorkflow()
    
    examples = [
        {
            "name": "快速排序算法",
            "task": """请编写一个 Python 函数，实现快速排序算法。
要求：
1. 函数名为 quick_sort
2. 接受一个列表作为参数
3. 返回排序后的列表
4. 包含适当的注释和文档字符串"""
        },
        {
            "name": "数据处理器",
            "task": """请编写一个 Python 数据处理器，用于处理 CSV 文件。
要求：
1. 类名为 CSVProcessor
2. 支持读取和写入 CSV 文件
3. 包含数据清洗方法"""
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n🎯 示例 {i}: {example['name']}")
        print("-" * 60)
        
        result = workflow.run_programming_task(example['task'])
        
        print(f"\n📊 示例 {i} 完成统计:")
        print(f"- 消息数量: {result['total_messages']}")
        print(f"- 任务类型: {result['task_type']}")
        print(f"- 状态: {result['status']}")
        
        if i < len(examples):
            print(f"\n⏳ 准备下一个示例...")
            workflow.reset_workflow()
            time.sleep(2)
    
    print(f"\n🎊 所有模拟示例完成!")


def interactive_mock_mode():
    """交互式模拟模式"""
    print("\n🎯 交互式模拟模式")
    print("-" * 50)
    print("请输入您的编程任务描述（输入 'quit' 退出）:")
    
    workflow = MockProgrammingWorkflow()
    
    while True:
        task_input = input("\n📝 任务描述: ").strip()
        
        if task_input.lower() in ['quit', 'exit', 'q']:
            print("👋 退出交互模式")
            break
        
        if not task_input:
            print("❌ 请输入有效的任务描述")
            continue
        
        print(f"\n🚀 开始处理任务: {task_input}")
        
        try:
            result = workflow.run_programming_task(task_input)
            print(f"\n✅ 任务完成! 总消息数: {result['total_messages']}")
            
            # 询问是否继续
            continue_input = input("\n是否继续下一个任务? (y/n): ").strip().lower()
            if continue_input not in ['y', 'yes']:
                break
                
            # 重置工作流
            workflow.reset_workflow()
            
        except Exception as e:
            print(f"❌ 任务执行出错: {e}")


def main():
    """主函数"""
    print("🎭 AutoGen 编程工作流模拟器")
    print("请选择运行模式:")
    print("1. 运行预设示例")
    print("2. 交互模式")
    print("3. 单独运行快速排序示例")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        run_mock_examples()
    elif choice == "2":
        interactive_mock_mode()
    elif choice == "3":
        workflow = MockProgrammingWorkflow()
        task = """请编写一个 Python 函数，实现快速排序算法。
要求：
1. 函数名为 quick_sort
2. 接受一个列表作为参数
3. 返回排序后的列表
4. 包含适当的注释和文档字符串"""
        workflow.run_programming_task(task)
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
