"""
AutoGen 编程工作流演示版本
展示工作流结构和配置，无需 API 密钥
"""
import autogen
from config import Config


def demo_workflow_structure():
    """演示工作流结构"""
    print("🎯 AutoGen 编程工作流演示")
    print("=" * 60)
    
    print("\n📋 工作流配置:")
    print(f"- 最大轮次: {Config.MAX_ROUNDS}")
    print(f"- 终止关键词: {Config.TERMINATION_KEYWORDS}")
    print(f"- 启用流式输出: {Config.ENABLE_STREAMING}")
    
    print("\n🤖 Agent 配置:")
    for agent_name, agent_config in Config.AGENT_CONFIG.items():
        print(f"\n{agent_config['name']}:")
        print(f"  角色: {agent_name}")
        print(f"  系统消息长度: {len(agent_config['system_message'])} 字符")
        
        # 显示系统消息的前几行
        lines = agent_config['system_message'].strip().split('\n')
        print(f"  主要职责: {lines[0] if lines else '未定义'}")
    
    print("\n🔄 工作流程:")
    print("1. 用户提供编程任务描述")
    print("2. CodeWriter 编写初始代码")
    print("3. CodeReviewer 审查代码并提出建议")
    print("4. CodeOptimizer 根据代码和建议进行优化")
    print("5. 重复步骤 2-4 直到满足终止条件")
    
    print("\n📊 预期输出:")
    print("- 完整的代码实现")
    print("- 详细的代码审查意见")
    print("- 优化后的最终代码")
    print("- 完整的对话历史")


def demo_agent_creation():
    """演示 Agent 创建过程（不需要 API 密钥）"""
    print("\n🛠️ Agent 创建演示")
    print("-" * 40)
    
    # 模拟 LLM 配置（不包含真实 API 密钥）
    demo_llm_config = {
        "model": "gpt-4",
        "api_key": "demo-key-placeholder",
        "temperature": 0.1,
    }
    
    print("📝 创建 CodeWriter Agent...")
    print(f"  - 名称: {Config.AGENT_CONFIG['code_writer']['name']}")
    print(f"  - 模型配置: {demo_llm_config['model']}")
    print(f"  - 温度参数: {demo_llm_config['temperature']}")
    
    print("\n🔍 创建 CodeReviewer Agent...")
    print(f"  - 名称: {Config.AGENT_CONFIG['code_reviewer']['name']}")
    print(f"  - 专注于: 代码质量审查")
    
    print("\n⚡ 创建 CodeOptimizer Agent...")
    print(f"  - 名称: {Config.AGENT_CONFIG['code_optimizer']['name']}")
    print(f"  - 专注于: 代码优化和改进")
    
    print("\n🎭 创建群聊管理器...")
    print("  - 轮询式发言顺序")
    print(f"  - 最大轮次: {Config.MAX_ROUNDS}")
    print("  - 自动终止条件")


def demo_task_examples():
    """演示任务示例"""
    print("\n📝 任务示例")
    print("-" * 40)
    
    examples = [
        {
            "name": "快速排序算法",
            "description": "实现高效的快速排序算法，包含完整的文档和测试",
            "complexity": "中等"
        },
        {
            "name": "网页爬虫类",
            "description": "创建通用的网页爬虫，支持多种配置和错误处理",
            "complexity": "高"
        },
        {
            "name": "数据处理器",
            "description": "CSV 文件处理工具，包含清洗、分析和可视化功能",
            "complexity": "高"
        },
        {
            "name": "API 客户端",
            "description": "REST API 客户端，支持认证、重试和异步请求",
            "complexity": "中等"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['name']}")
        print(f"   描述: {example['description']}")
        print(f"   复杂度: {example['complexity']}")


def demo_expected_workflow():
    """演示预期的工作流对话"""
    print("\n💬 预期工作流对话示例")
    print("-" * 40)
    
    print("\n👤 用户:")
    print("请编写一个 Python 函数，实现快速排序算法。")
    
    print("\n🖥️ CodeWriter:")
    print("我将为您编写一个快速排序函数...")
    print("```python")
    print("def quick_sort(arr):")
    print("    # 实现快速排序算法")
    print("    ...")
    print("```")
    
    print("\n🔍 CodeReviewer:")
    print("代码审查意见：")
    print("1. 建议添加类型注解")
    print("2. 需要处理边界情况")
    print("3. 可以优化性能...")
    
    print("\n⚡ CodeOptimizer:")
    print("根据审查建议，我将优化代码：")
    print("```python")
    print("def quick_sort(arr: List[int]) -> List[int]:")
    print("    # 优化后的实现")
    print("    ...")
    print("```")
    print("OPTIMIZATION_COMPLETE")


def main():
    """主演示函数"""
    print("🚀 AutoGen 编程工作流完整演示")
    print("=" * 80)
    
    # 演示各个组件
    demo_workflow_structure()
    demo_agent_creation()
    demo_task_examples()
    demo_expected_workflow()
    
    print("\n" + "=" * 80)
    print("✅ 演示完成!")
    print("\n📌 要运行实际工作流，请：")
    print("1. 设置 OPENAI_API_KEY 环境变量")
    print("2. 运行: python programming_workflow.py")
    print("3. 或运行: python example_usage.py")
    
    print("\n🔧 环境设置:")
    print("export OPENAI_API_KEY='your-api-key-here'")
    print("python programming_workflow.py")


if __name__ == "__main__":
    main()
