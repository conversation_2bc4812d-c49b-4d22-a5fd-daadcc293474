# AutoGen 编程工作流

基于 Microsoft AutoGen 框架的多智能体编程工作流，实现代码编写、审查和优化的自动化协作。

## 功能特性

- **三个专业 Agent 协作**：
  - 🖥️ **CodeWriter**: 负责编写高质量代码
  - 🔍 **CodeReviewer**: 审查代码并提出改进建议  
  - ⚡ **CodeOptimizer**: 根据代码和建议进行优化

- **智能工作流**：
  - 轮询式多智能体对话
  - 自动终止条件
  - 完整的编程生命周期

- **易于使用**：
  - 简单的 API 接口
  - 多种使用示例
  - 交互式模式

## 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd ai-test
```

2. 创建虚拟环境：
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 设置环境变量：
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

## 快速开始

### 基本使用

```python
from programming_workflow import run_quick_programming_task

# 定义编程任务
task = """
请编写一个 Python 函数，实现快速排序算法。
要求：
1. 函数名为 quick_sort
2. 接受一个列表作为参数
3. 返回排序后的列表
4. 包含适当的注释和文档字符串
"""

# 运行工作流
result = run_quick_programming_task(task)
print(f"完成! 总消息数: {result['total_messages']}")
```

### 高级使用

```python
from programming_workflow import ProgrammingWorkflow

# 创建工作流实例
workflow = ProgrammingWorkflow()
workflow.initialize()

# 运行任务
result = workflow.run_programming_task("编写一个网页爬虫类")

# 重置并运行新任务
workflow.reset_workflow()
result2 = workflow.run_programming_task("优化上面的爬虫代码")
```

## 运行示例

### 1. 快速开始（推荐）
```bash
# 运行自动设置脚本
python setup.py

# 运行演示（无需 API 密钥）
source venv/bin/activate
python demo_workflow.py

# 运行模拟工作流（无需 API 密钥）
python mock_workflow.py
```

### 2. 运行真实工作流（需要 API 密钥）
```bash
# 设置 API 密钥
export OPENAI_API_KEY="your-api-key-here"

# 运行示例程序
source venv/bin/activate
python example_usage.py

# 或直接运行工作流
python programming_workflow.py
```

## 项目结构

```
ai-test/
├── requirements.txt          # 项目依赖
├── config.py                # 配置文件
├── programming_workflow.py   # 主要工作流实现（需要 API 密钥）
├── example_usage.py         # 使用示例（需要 API 密钥）
├── demo_workflow.py         # 演示版本（无需 API 密钥）
├── mock_workflow.py         # 模拟工作流（无需 API 密钥）
├── setup.py                # 自动设置脚本
├── .env.example            # 环境变量模板
├── .env                    # 环境变量文件（自动生成）
├── README.md               # 项目说明
└── venv/                   # 虚拟环境
```

## 配置说明

在 `config.py` 中可以自定义：

- **模型配置**: OpenAI 模型和 API 密钥
- **Agent 配置**: 每个 Agent 的系统消息和行为
- **工作流配置**: 最大轮次、终止条件等

## 示例任务

项目包含以下预设示例：

1. **快速排序算法**: 实现经典排序算法
2. **网页爬虫**: 创建通用爬虫类
3. **数据处理器**: CSV 文件处理工具
4. **API 客户端**: REST API 客户端实现

## 工作流程

1. **CodeWriter** 根据需求编写初始代码
2. **CodeReviewer** 审查代码，提出改进建议
3. **CodeOptimizer** 根据原代码和建议进行优化
4. 循环直到满足终止条件

## 技术栈

- **AutoGen 0.2**: 多智能体框架
- **OpenAI GPT**: 语言模型
- **Python 3.9+**: 编程语言

## 注意事项

- 需要有效的 OpenAI API 密钥
- 建议使用 GPT-4 模型以获得最佳效果
- 网络连接需要能访问 OpenAI API

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
