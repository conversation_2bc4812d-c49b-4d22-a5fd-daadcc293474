#!/usr/bin/env python3
"""
AutoGen 编程工作流设置脚本
帮助用户快速配置环境和 API 密钥
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"🐍 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ 需要 Python 3.9 或更高版本")
        return False
    
    print("✅ Python 版本符合要求")
    return True


def check_venv():
    """检查虚拟环境"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ 虚拟环境已存在")
        return True
    else:
        print("📦 创建虚拟环境...")
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print("✅ 虚拟环境创建成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ 虚拟环境创建失败")
            return False


def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    
    # 确定 pip 路径
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_path = "venv/bin/pip"
    
    try:
        # 升级 pip
        subprocess.run([pip_path, "install", "--upgrade", "pip"], check=True)
        
        # 安装依赖
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False


def setup_env_file():
    """设置环境变量文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env 文件已存在")
        return True
    
    if env_example.exists():
        print("📝 创建 .env 文件...")
        
        # 读取示例文件
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提示用户输入 API 密钥
        print("\n🔑 请输入您的 OpenAI API 密钥:")
        print("(如果暂时没有，可以直接回车，稍后手动编辑 .env 文件)")
        
        api_key = input("API Key: ").strip()
        
        if api_key:
            content = content.replace("your-openai-api-key-here", api_key)
        
        # 写入 .env 文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ .env 文件创建成功")
        
        if not api_key:
            print("⚠️  请编辑 .env 文件，设置您的 OpenAI API 密钥")
        
        return True
    else:
        print("❌ .env.example 文件不存在")
        return False


def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    # 确定 python 路径
    if os.name == 'nt':  # Windows
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/Mac
        python_path = "venv/bin/python"
    
    try:
        # 测试导入
        result = subprocess.run([
            python_path, "-c", 
            "import autogen; from programming_workflow import ProgrammingWorkflow; print('✅ 导入成功')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 安装测试通过")
            return True
        else:
            print(f"❌ 导入测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("🎉 设置完成！")
    print("="*60)
    
    print("\n📖 使用说明:")
    
    # 激活虚拟环境的命令
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
    else:  # Unix/Linux/Mac
        activate_cmd = "source venv/bin/activate"
    
    print(f"\n1. 激活虚拟环境:")
    print(f"   {activate_cmd}")
    
    print(f"\n2. 运行演示:")
    print(f"   python demo_workflow.py")
    
    print(f"\n3. 运行示例 (需要 API 密钥):")
    print(f"   python example_usage.py")
    
    print(f"\n4. 直接运行工作流:")
    print(f"   python programming_workflow.py")
    
    print(f"\n📝 编辑配置:")
    print(f"   编辑 .env 文件设置 API 密钥")
    print(f"   编辑 config.py 自定义 Agent 行为")
    
    print(f"\n🔗 项目文件:")
    print(f"   - README.md: 详细文档")
    print(f"   - demo_workflow.py: 无需 API 的演示")
    print(f"   - example_usage.py: 完整使用示例")


def main():
    """主设置函数"""
    print("🚀 AutoGen 编程工作流设置向导")
    print("="*60)
    
    # 检查步骤
    steps = [
        ("检查 Python 版本", check_python_version),
        ("设置虚拟环境", check_venv),
        ("安装依赖包", install_dependencies),
        ("配置环境变量", setup_env_file),
        ("测试安装", test_installation),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，请检查错误信息")
            sys.exit(1)
    
    # 显示使用说明
    show_usage_instructions()


if __name__ == "__main__":
    main()
