"""
AutoGen 编程工作流使用示例
展示如何使用编程工作流来完成不同类型的编程任务
"""
import asyncio
import os
from programming_workflow import ProgrammingWorkflow, run_quick_programming_task


async def example_1_quick_sort():
    """示例1: 快速排序算法实现"""
    print("🔥 示例1: 快速排序算法实现")
    print("-" * 50)
    
    task = """
    请编写一个 Python 函数，实现快速排序算法。
    要求：
    1. 函数名为 quick_sort
    2. 接受一个列表作为参数
    3. 返回排序后的列表
    4. 包含适当的注释和文档字符串
    5. 处理边界情况（空列表、单元素列表）
    """
    
    result = await run_quick_programming_task(task)
    return result


async def example_2_web_scraper():
    """示例2: 网页爬虫实现"""
    print("\n🔥 示例2: 网页爬虫实现")
    print("-" * 50)
    
    workflow = await ProgrammingWorkflow().initialize()
    
    task = """
    请编写一个 Python 网页爬虫类，用于抓取网页内容。
    要求：
    1. 类名为 WebScraper
    2. 支持设置请求头和超时时间
    3. 包含获取网页内容的方法
    4. 包含解析 HTML 的基础方法
    5. 适当的错误处理和异常管理
    6. 使用 requests 和 BeautifulSoup 库
    """
    
    try:
        result = await workflow.run_programming_task(task)
        return result
    finally:
        await workflow.close()


async def example_3_data_processor():
    """示例3: 数据处理器实现"""
    print("\n🔥 示例3: 数据处理器实现")
    print("-" * 50)
    
    workflow = ProgrammingWorkflow()
    await workflow.initialize()
    
    task = """
    请编写一个 Python 数据处理器，用于处理 CSV 文件。
    要求：
    1. 类名为 CSVProcessor
    2. 支持读取和写入 CSV 文件
    3. 包含数据清洗方法（去除空值、重复值）
    4. 包含基础统计分析方法
    5. 支持数据筛选和排序
    6. 使用 pandas 库
    7. 包含完整的错误处理
    """
    
    try:
        result = await workflow.run_programming_task(task, stream_output=True)
        
        # 重置工作流，准备下一个任务
        await workflow.reset_workflow()
        
        # 继续优化任务
        optimization_task = """
        请进一步优化上面的 CSVProcessor 类：
        1. 添加数据可视化方法
        2. 支持多种文件格式（Excel, JSON）
        3. 添加数据验证功能
        4. 优化性能，支持大文件处理
        """
        
        print("\n🔧 继续优化任务...")
        result2 = await workflow.run_programming_task(optimization_task)
        
        return result, result2
    finally:
        await workflow.close()


async def example_4_api_client():
    """示例4: API 客户端实现"""
    print("\n🔥 示例4: REST API 客户端实现")
    print("-" * 50)
    
    task = """
    请编写一个通用的 REST API 客户端类。
    要求：
    1. 类名为 APIClient
    2. 支持 GET, POST, PUT, DELETE 方法
    3. 支持认证（API Key, Bearer Token）
    4. 包含请求重试机制
    5. 支持请求和响应的日志记录
    6. 适当的错误处理和异常定义
    7. 支持异步请求
    8. 使用 aiohttp 库
    """
    
    result = await run_quick_programming_task(task)
    return result


async def interactive_mode():
    """交互模式：用户可以输入自定义任务"""
    print("\n🎯 交互模式")
    print("-" * 50)
    print("请输入您的编程任务描述（输入 'quit' 退出）:")
    
    workflow = ProgrammingWorkflow()
    await workflow.initialize()
    
    try:
        while True:
            task_input = input("\n📝 任务描述: ").strip()
            
            if task_input.lower() in ['quit', 'exit', 'q']:
                print("👋 退出交互模式")
                break
            
            if not task_input:
                print("❌ 请输入有效的任务描述")
                continue
            
            print(f"\n🚀 开始处理任务: {task_input}")
            
            try:
                result = await workflow.run_programming_task(task_input)
                print(f"\n✅ 任务完成! 停止原因: {result.stop_reason}")
                
                # 询问是否继续
                continue_input = input("\n是否继续下一个任务? (y/n): ").strip().lower()
                if continue_input not in ['y', 'yes']:
                    break
                    
                # 重置工作流
                await workflow.reset_workflow()
                
            except Exception as e:
                print(f"❌ 任务执行出错: {e}")
                
    finally:
        await workflow.close()


async def run_all_examples():
    """运行所有示例"""
    print("🎉 AutoGen 编程工作流示例集合")
    print("=" * 80)
    
    # 检查 API Key
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        return
    
    examples = [
        ("快速排序算法", example_1_quick_sort),
        ("网页爬虫", example_2_web_scraper),
        ("数据处理器", example_3_data_processor),
        ("API 客户端", example_4_api_client)
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n🎯 正在运行示例: {name}")
            await example_func()
            print(f"✅ 示例 '{name}' 完成")
        except Exception as e:
            print(f"❌ 示例 '{name}' 执行失败: {e}")
        
        # 等待一下，避免 API 限制
        await asyncio.sleep(2)
    
    print("\n🎊 所有示例执行完成!")


async def main():
    """主函数"""
    print("AutoGen 编程工作流示例")
    print("请选择运行模式:")
    print("1. 运行所有示例")
    print("2. 交互模式")
    print("3. 单独运行快速排序示例")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        await run_all_examples()
    elif choice == "2":
        await interactive_mode()
    elif choice == "3":
        await example_1_quick_sort()
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    # 设置事件循环策略（Windows 兼容性）
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
