"""
AutoGen 编程工作流实现
使用最新的 AutoGen AgentChat API 创建三个协作的编程 Agent
"""
import asyncio
from typing import List, Optional, Dict, Any
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_agentchat.base import TaskResult
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import Config


class ProgrammingWorkflow:
    """编程工作流类"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4o"):
        """
        初始化编程工作流
        
        Args:
            api_key: OpenAI API 密钥
            model: 使用的模型名称
        """
        self.api_key = api_key or Config.OPENAI_API_KEY
        self.model = model
        self.model_client = None
        self.agents = {}
        self.team = None
        
        if not self.api_key:
            raise ValueError("请设置 OPENAI_API_KEY 环境变量或传入 api_key 参数")
    
    async def initialize(self):
        """初始化模型客户端和 agents"""
        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=self.model,
            api_key=self.api_key
        )
        
        # 创建三个 agents
        self._create_agents()
        
        # 创建团队
        self._create_team()
    
    def _create_agents(self):
        """创建三个编程 agents"""
        agent_configs = Config.AGENT_CONFIG
        
        # Agent 1: 代码编写者
        self.agents["code_writer"] = AssistantAgent(
            name=agent_configs["code_writer"]["name"],
            model_client=self.model_client,
            system_message=agent_configs["code_writer"]["system_message"]
        )
        
        # Agent 2: 代码审查者
        self.agents["code_reviewer"] = AssistantAgent(
            name=agent_configs["code_reviewer"]["name"],
            model_client=self.model_client,
            system_message=agent_configs["code_reviewer"]["system_message"]
        )
        
        # Agent 3: 代码优化者
        self.agents["code_optimizer"] = AssistantAgent(
            name=agent_configs["code_optimizer"]["name"],
            model_client=self.model_client,
            system_message=agent_configs["code_optimizer"]["system_message"]
        )
    
    def _create_team(self):
        """创建团队和终止条件"""
        # 创建终止条件
        text_termination = TextMentionTermination("OPTIMIZATION_COMPLETE")
        max_message_termination = MaxMessageTermination(Config.MAX_ROUNDS)
        
        # 组合终止条件
        termination_condition = text_termination | max_message_termination
        
        # 创建团队 - 按照工作流顺序：编写 -> 审查 -> 优化
        agent_list = [
            self.agents["code_writer"],
            self.agents["code_reviewer"], 
            self.agents["code_optimizer"]
        ]
        
        self.team = RoundRobinGroupChat(
            participants=agent_list,
            termination_condition=termination_condition
        )
    
    async def run_programming_task(self, task_description: str, stream_output: bool = True) -> TaskResult:
        """
        运行编程任务
        
        Args:
            task_description: 编程任务描述
            stream_output: 是否流式输出
            
        Returns:
            TaskResult: 任务结果
        """
        if not self.team:
            await self.initialize()
        
        print(f"🚀 开始编程工作流...")
        print(f"📝 任务描述: {task_description}")
        print("=" * 80)
        
        if stream_output:
            # 流式输出
            result = await Console(
                self.team.run_stream(task=task_description)
            )
        else:
            # 非流式输出
            result = await self.team.run(task=task_description)
        
        print("=" * 80)
        print(f"✅ 工作流完成!")
        print(f"🔚 停止原因: {result.stop_reason}")
        print(f"💬 总消息数: {len(result.messages)}")
        
        return result
    
    async def reset_workflow(self):
        """重置工作流状态"""
        if self.team:
            await self.team.reset()
            print("🔄 工作流已重置")
    
    async def close(self):
        """关闭资源"""
        if self.model_client:
            await self.model_client.close()
            print("🔒 资源已关闭")
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """获取工作流摘要信息"""
        return {
            "model": self.model,
            "agents": list(self.agents.keys()) if self.agents else [],
            "max_rounds": Config.MAX_ROUNDS,
            "termination_keywords": Config.TERMINATION_KEYWORDS
        }


# 便捷函数
async def create_programming_workflow(api_key: Optional[str] = None, model: str = "gpt-4o") -> ProgrammingWorkflow:
    """
    创建并初始化编程工作流
    
    Args:
        api_key: OpenAI API 密钥
        model: 模型名称
        
    Returns:
        ProgrammingWorkflow: 初始化后的工作流实例
    """
    workflow = ProgrammingWorkflow(api_key=api_key, model=model)
    await workflow.initialize()
    return workflow


async def run_quick_programming_task(task_description: str, api_key: Optional[str] = None) -> TaskResult:
    """
    快速运行编程任务的便捷函数
    
    Args:
        task_description: 任务描述
        api_key: OpenAI API 密钥
        
    Returns:
        TaskResult: 任务结果
    """
    workflow = await create_programming_workflow(api_key=api_key)
    try:
        result = await workflow.run_programming_task(task_description)
        return result
    finally:
        await workflow.close()


if __name__ == "__main__":
    # 示例用法
    async def main():
        # 示例任务
        task = """
        请编写一个 Python 函数，实现快速排序算法。
        要求：
        1. 函数名为 quick_sort
        2. 接受一个列表作为参数
        3. 返回排序后的列表
        4. 包含适当的注释和文档字符串
        """
        
        # 运行工作流
        result = await run_quick_programming_task(task)
        
        print("\n" + "="*50)
        print("📊 最终结果摘要:")
        print(f"消息总数: {len(result.messages)}")
        print(f"停止原因: {result.stop_reason}")
    
    # 运行示例
    asyncio.run(main())
